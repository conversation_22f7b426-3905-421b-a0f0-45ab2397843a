creator dashboard is not mobile responsive and content is not visible cause navigation takes up too much space. make simple changes. dont change too much.

the mobile: go live view:  the end stream button above the camera preview is overlapping with the text before and going out of view. make simple changes. dont change too much.

the mobile: go live view:  make the text you are live above the preview a one liner and no card. remove the you are live part and leave only the notice. make simple changes. dont change too much.

the mobile: go live view (when broadcasting) is  slightly zoomed in on mobile when entering.

remove broadcast


when starting a new stream on mobile (phantom wallet inapp browser ios) it doesnt start  active broadcast on livepeer. on livepeer its only idle andlogs it says: Waiting for events...    
starting it first time worked fine now it doesnt work at all. the streams overview page also doesnt show it as live and the stream detail page shows stream ended.   

the preview on go-live above the real time chat doesnt show the stream video also