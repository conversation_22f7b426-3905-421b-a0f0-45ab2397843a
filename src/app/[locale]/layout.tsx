import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { NextIntlClientProvider } from 'next-intl';
import { notFound } from 'next/navigation';
import { locales, defaultLocale } from '@/lib/i18n';
import { SolanaWalletProvider } from '@/components/wallet/WalletProvider';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import CookieConsent from '@/components/cookies/CookieConsent';
import { ToastProvider } from '@/components/ui/toast';
import { ConditionalAnalytics } from '@/components/ConditionalAnalytics';
import type { Locale } from '@/lib/i18n';
import '@/app/globals.css';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'bonkstream',
  description: 'Web3-native streaming platform with BONK tips and NFT moments',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

// Load all translation namespaces for client provider
async function loadAllMessages(locale: string) {
  const validLocale = locales.includes(locale as Locale) ? locale : defaultLocale;
  
  // Load namespace with proper error handling
  async function loadNamespace(namespace: string) {
    try {
      return (await import(`../../../src/locales/${validLocale}/${namespace}.json`)).default;
    } catch (error) {
      // Try the default locale if the current one fails
      if (validLocale !== defaultLocale) {
        try {
          return (await import(`../../../src/locales/${defaultLocale}/${namespace}.json`)).default;
        } catch (secondError) {
          console.error(`Failed to load ${namespace} messages for ${validLocale} and ${defaultLocale}:`, secondError);
          return {};
        }
      }
      console.error(`Failed to load ${namespace} messages for ${validLocale}:`, error);
      return {};
    }
  }
  
  // Load all available namespaces
  const [
    common,
    admin,
    stream,
    streaming,
    profile,
    collection,
    creator,
    creatorPublicPage,
    legal
  ] = await Promise.all([
    loadNamespace('common'),
    loadNamespace('admin'),
    loadNamespace('stream'),
    loadNamespace('streaming'),
    loadNamespace('profile'),
    loadNamespace('collection'),
    loadNamespace('creator'),
    loadNamespace('creatorPublicPage'),
    loadNamespace('legal')
  ]);
  
  return {
    common,
    admin,
    stream,
    streaming,
    profile,
    collection,
    creator,
    creatorPublicPage,
    legal
  };
}

interface Props {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({ children, params }: Props) {
  // Next.js 15 requires params to be properly awaited
  const resolvedParams = await params;
  
  const locale = (resolvedParams?.locale && typeof resolvedParams.locale === 'string' && 
                  locales.includes(resolvedParams.locale as Locale)) 
    ? resolvedParams.locale 
    : defaultLocale;
  
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  const messages = await loadAllMessages(locale);

  const timeZone = 'Europe/Berlin';

  return (
    <html lang={locale} className={`${inter.className} dark`}>
      <body>
        <NextIntlClientProvider locale={locale} messages={messages} timeZone={timeZone}>
          <SolanaWalletProvider>
            <ToastProvider>
              <ConditionalAnalytics />
              <div className="bg-black text-white flex min-h-screen flex-col">
                <Header />
                <main className="flex-grow">{children}</main>
                <Footer />
              </div>
              <CookieConsent />
            </ToastProvider>
          </SolanaWalletProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
} 